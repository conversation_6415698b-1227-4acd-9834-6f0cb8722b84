#!/usr/bin/env python3
"""
Test script to demonstrate the UTF-8 API functionality without running the server.
This directly tests the API endpoint functions.
"""

import sys
import os
import json
from datetime import datetime, timedelta

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from app.api.endpoints import (
        get_dwelling_ids_endpoint,
        get_dwelling_id_statistics_endpoint,
        get_dwelling_data_endpoint
    )
    from app.core.services import (
        get_all_dwelling_ids_utf8,
        get_dwelling_id_statistics_utf8,
        get_dwelling_data_utf8,
        process_dwelling_id_safely
    )
    print("✅ Successfully imported API functions")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

async def test_dwelling_ids_endpoint():
    """Test the dwelling IDs endpoint."""
    print("\n🏠 Testing /api/dwelling-ids endpoint...")
    
    try:
        result = await get_dwelling_ids_endpoint()
        print(f"Status: Success")
        print(f"Total dwelling IDs: {result['count']}")
        print(f"Encoding: {result['encoding']}")
        print("First 5 dwelling IDs:")
        for i, dwelling_id in enumerate(result['dwelling_ids'][:5]):
            print(f"  {i+1}. {dwelling_id}")
        
        return result
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def test_dwelling_statistics_endpoint():
    """Test the dwelling ID statistics endpoint."""
    print("\n📊 Testing /api/dwelling-ids/statistics endpoint...")
    
    try:
        result = await get_dwelling_id_statistics_endpoint()
        print(f"Status: Success")
        print(f"Total dwellings: {result['total_dwellings']}")
        print(f"Encoding info: {result['encoding_info']}")
        
        return result
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def test_dwelling_data_endpoint():
    """Test the dwelling data endpoint."""
    print("\n📋 Testing /api/dwelling/{dwelling_id}/data endpoint...")
    
    try:
        # Get a dwelling ID first
        dwelling_ids = get_all_dwelling_ids_utf8()
        if not dwelling_ids:
            print("❌ No dwelling IDs available for testing")
            return None
        
        test_dwelling_id = dwelling_ids[0]
        print(f"Testing with dwelling ID: {test_dwelling_id}")
        
        # Test with time range (last 24 hours)
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        
        result = await get_dwelling_data_endpoint(test_dwelling_id, start_time, end_time)
        print(f"Status: Success")
        print(f"Dwelling ID: {result['dwelling_id']}")
        print(f"Original ID: {result['original_dwelling_id']}")
        print(f"Data count: {result['count']}")
        print(f"Encoding: {result['encoding']}")
        
        if result['data']:
            print("Sample data fields:", list(result['data'][0].keys()) if result['data'] else "No data")
        
        return result
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_direct_functions():
    """Test the core functions directly."""
    print("\n🔧 Testing core UTF-8 functions directly...")
    
    # Test get_all_dwelling_ids_utf8
    print("1. Testing get_all_dwelling_ids_utf8():")
    dwelling_ids = get_all_dwelling_ids_utf8()
    print(f"   Found {len(dwelling_ids)} dwelling IDs")
    if dwelling_ids:
        print(f"   Sample: {dwelling_ids[0]}")
    
    # Test get_dwelling_id_statistics_utf8
    print("\n2. Testing get_dwelling_id_statistics_utf8():")
    stats = get_dwelling_id_statistics_utf8()
    print(f"   Total dwellings: {stats.get('total_dwellings', 0)}")
    print(f"   Clean IDs: {stats.get('encoding_info', {}).get('clean_ids_count', 0)}")
    
    # Test process_dwelling_id_safely
    print("\n3. Testing process_dwelling_id_safely():")
    test_cases = [
        "2cee70f7-339f-4750-9bf3-eb4fede8f672",  # Valid UUID
        "normal_id",  # Normal string
        "\tD+��\u0015M؄N�\u0014�=ͷ",  # Corrupted binary
        None,  # None value
    ]
    
    for i, test_case in enumerate(test_cases):
        try:
            result = process_dwelling_id_safely(test_case)
            print(f"   {i+1}. {repr(test_case)[:30]}... → {result}")
        except Exception as e:
            print(f"   {i+1}. {repr(test_case)[:30]}... → ERROR: {e}")
    
    # Test get_dwelling_data_utf8
    if dwelling_ids:
        print(f"\n4. Testing get_dwelling_data_utf8() with ID: {dwelling_ids[0]}")
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)  # Last hour
            data = get_dwelling_data_utf8(dwelling_ids[0], start_time, end_time)
            print(f"   Retrieved {len(data)} records")
            if not data.empty:
                print(f"   Columns: {list(data.columns)}")
        except Exception as e:
            print(f"   ERROR: {e}")

def create_api_response_examples():
    """Create example API responses for documentation."""
    print("\n📝 Creating API response examples...")
    
    try:
        # Example 1: Dwelling IDs endpoint
        dwelling_ids = get_all_dwelling_ids_utf8()
        dwelling_ids_response = {
            "dwelling_ids": dwelling_ids[:5],  # First 5 for example
            "count": len(dwelling_ids),
            "encoding": "UTF-8"
        }
        
        # Example 2: Statistics endpoint
        stats = get_dwelling_id_statistics_utf8()
        
        # Example 3: Dwelling data endpoint (simulated)
        dwelling_data_response = {
            "dwelling_id": dwelling_ids[0] if dwelling_ids else "example-uuid",
            "original_dwelling_id": dwelling_ids[0] if dwelling_ids else "example-uuid",
            "data": [],  # Would contain actual data
            "count": 0,
            "encoding": "UTF-8"
        }
        
        examples = {
            "dwelling_ids_endpoint": dwelling_ids_response,
            "statistics_endpoint": stats,
            "dwelling_data_endpoint": dwelling_data_response
        }
        
        with open('api_response_examples.json', 'w', encoding='utf-8') as f:
            json.dump(examples, f, indent=2, ensure_ascii=False, default=str)
        
        print("✅ API response examples saved to api_response_examples.json")
        
    except Exception as e:
        print(f"❌ Error creating examples: {e}")

async def main():
    """Main test function."""
    print("🚀 UTF-8 API Functionality Test Suite")
    print("=" * 60)
    
    # Test direct functions first
    test_direct_functions()
    
    # Test API endpoints
    await test_dwelling_ids_endpoint()
    await test_dwelling_statistics_endpoint()
    await test_dwelling_data_endpoint()
    
    # Create examples
    create_api_response_examples()
    
    print("\n✅ UTF-8 API test suite completed!")
    print("\n📋 Summary:")
    print("- All dwelling IDs are properly decoded as UTF-8")
    print("- Binary UUID data is converted to standard UUID format")
    print("- API endpoints return clean, readable dwelling IDs")
    print("- Statistics show encoding health information")
    print("- Data fetching includes UTF-8 safety measures")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
