import datetime
from pymongo import MongoClient
from bson.codec_options import CodecOptions
from app.core.config import settings
import time
import pandas as pd
from typing import List, Dict, Any, Optional

def get_db():
    max_retries = 5
    retry_delay = 5  # seconds

    for attempt in range(max_retries):
        try:
            client = MongoClient(
                settings.MONGO_URI,
                connectTimeoutMS=30000,  # 30 seconds
                serverSelectionTimeoutMS=30000,  # 30 seconds
                socketTimeoutMS=60000  # 60 seconds
            )
            # Verify connection
            client.admin.command('ping')
            print("✅ MongoDB connection successful")

            # Configure database with UTF-8 codec options
            codec_options = CodecOptions(
                unicode_decode_error_handler='strict',
                document_class=dict
            )

            return client.get_database(settings.DB_NAME, codec_options=codec_options)
        except Exception as e:
            if attempt == max_retries - 1:
                raise Exception(f"Failed to connect to MongoDB after {max_retries} attempts: {str(e)}")
            print(f"⚠️ MongoDB connection failed (attempt {attempt + 1}), retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)

def decode_utf8_safely(value: Any) -> str:
    """
    Safely decode a value to UTF-8 string format.
    Handles various input types including binary UUIDs and ensures proper UTF-8 encoding.
    """
    if value is None:
        return ""

    # Handle bytes input (could be binary UUID or other binary data)
    if isinstance(value, bytes):
        try:
            # First try to decode as UTF-8
            decoded = value.decode('utf-8', errors='ignore')
            # If it contains mostly control characters, it's likely binary data
            if len([c for c in decoded if ord(c) < 32 and c not in '\t\n\r']) > len(decoded) * 0.3:
                # Try to interpret as UUID bytes (16 bytes)
                if len(value) == 16:
                    import uuid
                    try:
                        uuid_obj = uuid.UUID(bytes=value)
                        return str(uuid_obj)
                    except ValueError:
                        pass
                # If not UUID, convert to hex representation
                return value.hex()
            return decoded
        except (UnicodeDecodeError, AttributeError):
            # Fallback to hex representation for binary data
            return value.hex() if hasattr(value, 'hex') else str(value)

    if isinstance(value, str):
        # Check if string contains binary/control characters
        control_chars = sum(1 for c in value if ord(c) < 32 and c not in '\t\n\r\x0b\x0c')
        if control_chars > len(value) * 0.3:  # If more than 30% are control chars
            try:
                # Try to interpret as UUID string with binary chars
                # Convert to bytes and then try UUID interpretation
                value_bytes = value.encode('latin1', errors='ignore')
                if len(value_bytes) == 16:
                    import uuid
                    try:
                        uuid_obj = uuid.UUID(bytes=value_bytes)
                        return str(uuid_obj)
                    except ValueError:
                        pass
                # Fallback to hex representation
                return value_bytes.hex()
            except Exception:
                pass

        # For normal strings, ensure proper UTF-8 encoding
        try:
            return value.encode('utf-8', errors='replace').decode('utf-8', errors='replace')
        except (UnicodeEncodeError, UnicodeDecodeError):
            return str(value)

    # Handle UUID objects directly
    if hasattr(value, 'hex') and hasattr(value, 'bytes'):
        try:
            import uuid
            if isinstance(value, uuid.UUID):
                return str(value)
        except:
            pass

    # For other types, convert to string and ensure UTF-8
    try:
        str_value = str(value)
        return str_value.encode('utf-8', errors='replace').decode('utf-8', errors='replace')
    except Exception:
        return str(value)

def decode_dwelling_id_advanced(value: Any) -> str:
    """
    Advanced dwelling ID decoder that handles various formats including binary UUIDs.

    Args:
        value: Raw dwelling ID value from database

    Returns:
        Properly decoded dwelling ID string
    """
    if value is None:
        return ""

    # Handle string input
    if isinstance(value, str):
        # Check if it's already a valid UUID format
        import re
        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        if re.match(uuid_pattern, value, re.IGNORECASE):
            return value

        # Check for binary characters in string
        if any(ord(c) < 32 and c not in '\t\n\r' for c in value):
            try:
                # Convert string to bytes using latin1 to preserve binary data
                value_bytes = value.encode('latin1')
                return decode_dwelling_id_advanced(value_bytes)
            except Exception:
                pass

        # Return as-is if it's a clean string
        return value

    # Handle bytes input
    if isinstance(value, bytes):
        # Try UUID interpretation first (16 bytes = UUID)
        if len(value) == 16:
            try:
                import uuid
                uuid_obj = uuid.UUID(bytes=value)
                return str(uuid_obj)
            except (ValueError, ImportError):
                pass

        # Try UTF-8 decoding
        try:
            decoded = value.decode('utf-8', errors='ignore')
            # If decoded string has minimal control characters, use it
            control_chars = sum(1 for c in decoded if ord(c) < 32 and c not in '\t\n\r')
            if control_chars < len(decoded) * 0.2:  # Less than 20% control chars
                return decoded
        except:
            pass

        # Fallback to hex representation
        return value.hex()

    # Handle other types
    return str(value)

def fetch_all_dwelling_ids(collection) -> List[str]:
    """
    Fetch all unique dwelling IDs from the database with proper UTF-8 decoding.

    Args:
        collection: MongoDB collection object

    Returns:
        List of UTF-8 decoded dwelling IDs
    """
    try:
        # Use aggregation pipeline to get distinct dwelling IDs
        pipeline = [
            {"$group": {"_id": "$dwellingid"}},
            {"$sort": {"_id": 1}}
        ]

        cursor = collection.aggregate(pipeline)
        dwelling_ids = []

        for doc in cursor:
            dwelling_id = doc.get('_id')
            if dwelling_id is not None:
                # Use advanced decoder for dwelling IDs
                decoded_id = decode_dwelling_id_advanced(dwelling_id)
                dwelling_ids.append(decoded_id)

        print(f"✅ Fetched {len(dwelling_ids)} unique dwelling IDs with UTF-8 decoding")
        return dwelling_ids

    except Exception as e:
        print(f"❌ Error fetching dwelling IDs: {str(e)}")
        return []

def fetch_data_with_utf8_ids(collection, query: Dict[str, Any], projection: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
    """
    Fetch data from MongoDB with proper UTF-8 decoding for all ID fields.

    Args:
        collection: MongoDB collection object
        query: MongoDB query dictionary
        projection: Optional projection dictionary

    Returns:
        DataFrame with UTF-8 decoded ID fields
    """
    try:
        # Default projection excludes _id if not specified
        if projection is None:
            projection = {"_id": 0}

        # Fetch data from MongoDB
        cursor = collection.find(query, projection)
        data_list = []

        for doc in cursor:
            # Decode dwelling ID using advanced decoder
            if 'dwellingid' in doc:
                doc['dwellingid'] = decode_dwelling_id_advanced(doc['dwellingid'])

            # Decode any other ID fields that might exist
            for key, value in doc.items():
                if isinstance(value, (str, bytes)) and ('id' in key.lower() or 'name' in key.lower()):
                    if 'dwellingid' in key.lower():
                        doc[key] = decode_dwelling_id_advanced(value)
                    else:
                        doc[key] = decode_utf8_safely(value)

            data_list.append(doc)

        # Convert to DataFrame
        df = pd.DataFrame(data_list)

        if not df.empty:
            print(f"✅ Fetched {len(df)} records with UTF-8 decoded IDs")

        return df

    except Exception as e:
        print(f"❌ Error fetching data with UTF-8 decoding: {str(e)}")
        return pd.DataFrame()

def get_dwelling_id_mapping(collection) -> Dict[str, str]:
    """
    Create a mapping of original dwelling IDs to UTF-8 decoded versions.
    Useful for maintaining consistency across the application.

    Args:
        collection: MongoDB collection object

    Returns:
        Dictionary mapping original IDs to UTF-8 decoded IDs
    """
    try:
        pipeline = [
            {"$group": {"_id": "$dwellingid"}},
            {"$sort": {"_id": 1}}
        ]

        cursor = collection.aggregate(pipeline)
        id_mapping = {}

        for doc in cursor:
            original_id = doc.get('_id')
            if original_id is not None:
                decoded_id = decode_dwelling_id_advanced(original_id)
                id_mapping[str(original_id)] = decoded_id

        print(f"✅ Created UTF-8 ID mapping for {len(id_mapping)} dwelling IDs")
        return id_mapping

    except Exception as e:
        print(f"❌ Error creating ID mapping: {str(e)}")
        return {}

# ============================================================================
# CREATE OPERATIONS
# ============================================================================

def insert_single_record(collection, record: Dict[str, Any]) -> Optional[str]:
    """
    Insert a single record into the collection.

    Args:
        collection: MongoDB collection object
        record: Dictionary containing the record data

    Returns:
        str: Inserted record ID if successful, None if failed
    """
    try:
        # Ensure dwelling ID is properly encoded
        if 'dwellingid' in record:
            record['dwellingid'] = decode_dwelling_id_advanced(record['dwellingid'])

        result = collection.insert_one(record)
        print(f"✅ Inserted record with ID: {result.inserted_id}")
        return str(result.inserted_id)

    except Exception as e:
        print(f"❌ Error inserting record: {str(e)}")
        return None

def insert_multiple_records(collection, records: List[Dict[str, Any]]) -> List[str]:
    """
    Insert multiple records into the collection.

    Args:
        collection: MongoDB collection object
        records: List of dictionaries containing record data

    Returns:
        List[str]: List of inserted record IDs
    """
    try:
        # Ensure dwelling IDs are properly encoded
        for record in records:
            if 'dwellingid' in record:
                record['dwellingid'] = decode_dwelling_id_advanced(record['dwellingid'])

        result = collection.insert_many(records)
        inserted_ids = [str(id) for id in result.inserted_ids]
        print(f"✅ Inserted {len(inserted_ids)} records")
        return inserted_ids

    except Exception as e:
        print(f"❌ Error inserting multiple records: {str(e)}")
        return []

def create_water_consumption_record(collection, dwelling_id: str, consumption: float,
                                  timestamp: Optional[datetime] = None) -> Optional[str]: # type: ignore
    """
    Create a new water consumption record.

    Args:
        collection: MongoDB collection object
        dwelling_id: The dwelling ID
        consumption: Water consumption value in L/min
        timestamp: Optional timestamp (defaults to current time)

    Returns:
        str: Inserted record ID if successful, None if failed
    """
    try:
        from datetime import datetime

        if timestamp is None:
            timestamp = datetime.utcnow()

        record = {
            'dwellingid': decode_dwelling_id_advanced(dwelling_id),
            'datatime': timestamp,
            'data': {
                'evt': {
                    'csm': consumption
                }
            },
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }

        result = collection.insert_one(record)
        print(f"✅ Created water consumption record for dwelling {dwelling_id}: {consumption} L/min")
        return str(result.inserted_id)

    except Exception as e:
        print(f"❌ Error creating water consumption record: {str(e)}")
        return None