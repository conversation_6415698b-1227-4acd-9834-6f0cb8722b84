from app.utils.thresholds import update_max_csm, max_csm_by_day,max_csm_by_hour
from sklearn.ensemble import IsolationForest
import joblib
import pandas as pd
import json
import os
from app.core.config import settings
from app.utils.thresholds import max_csm_by_hour, max_csm_by_day

# Global dwelling ID mapping
DWELLING_ID_MAPPING_FILE = "dwelling_id_mapping.json"
_dwelling_id_mapping = {}
_reverse_mapping = {}

def load_dwelling_id_mapping():
    """Load dwelling ID mapping from file."""
    global _dwelling_id_mapping, _reverse_mapping

    if os.path.exists(DWELLING_ID_MAPPING_FILE):
        try:
            with open(DWELLING_ID_MAPPING_FILE, 'r', encoding='utf-8') as f:
                _dwelling_id_mapping = json.load(f)
            # Create reverse mapping for decoding
            _reverse_mapping = {v: k for k, v in _dwelling_id_mapping.items()}
            print(f"✅ Loaded dwelling ID mapping with {len(_dwelling_id_mapping)} entries")
        except Exception as e:
            print(f"⚠️ Error loading dwelling ID mapping: {e}")
            _dwelling_id_mapping = {}
            _reverse_mapping = {}
    else:
        print("📝 No existing dwelling ID mapping found, will create new one")

def save_dwelling_id_mapping():
    """Save dwelling ID mapping to file."""
    try:
        with open(DWELLING_ID_MAPPING_FILE, 'w', encoding='utf-8') as f:
            json.dump(_dwelling_id_mapping, f, indent=2, ensure_ascii=False)
        print(f"💾 Saved dwelling ID mapping with {len(_dwelling_id_mapping)} entries")
    except Exception as e:
        print(f"❌ Error saving dwelling ID mapping: {e}")

def get_dwelling_id_number(dwelling_id):
    """
    Get a static number for a dwelling ID. Creates new mapping if ID not seen before.

    Args:
        dwelling_id: The dwelling ID string

    Returns:
        int: Static number for this dwelling ID
    """
    global _dwelling_id_mapping, _reverse_mapping

    # Convert to string to ensure consistency
    dwelling_id_str = str(dwelling_id)

    if dwelling_id_str not in _dwelling_id_mapping:
        # Assign next available number
        next_number = len(_dwelling_id_mapping) + 1
        _dwelling_id_mapping[dwelling_id_str] = next_number
        _reverse_mapping[next_number] = dwelling_id_str
        print(f"🆔 Assigned number {next_number} to dwelling ID: {dwelling_id_str}")

        # Save updated mapping
        save_dwelling_id_mapping()

    return _dwelling_id_mapping[dwelling_id_str]

def get_dwelling_id_from_number(number):
    """
    Get dwelling ID from its static number.

    Args:
        number: The static number

    Returns:
        str: The dwelling ID string, or None if not found
    """
    return _reverse_mapping.get(number)

def encode_dwelling_ids_static(data):
    """
    Encode dwelling IDs using static mapping instead of categorical codes.

    Args:
        data: DataFrame with 'dwellingid' column

    Returns:
        DataFrame with 'dwellingid' column replaced by static numbers
    """
    # Load mapping if not already loaded
    if not _dwelling_id_mapping:
        load_dwelling_id_mapping()

    # Create a copy to avoid modifying original data
    data_copy = data.copy()

    # Map dwelling IDs to static numbers
    data_copy["dwellingid"] = data_copy["dwellingid"].apply(get_dwelling_id_number)

    return data_copy

def train_anomaly_model(data, peak_hours):
    """Train and save anomaly detection model with static dwelling ID encoding"""
    if data.empty:
        return None

    print("🏠 Encoding dwelling IDs with static mapping...")

    # Feature engineering with static dwelling ID encoding
    data = encode_dwelling_ids_static(data)
    data["is_peak_hour"] = data["hour"].isin(peak_hours).astype(int)
    
    # Dynamic contamination
    q99 = data["data.evt.csm"].quantile(0.99)
    contamination = (data["data.evt.csm"] > q99).mean()
    contamination = max(min(contamination, 0.1), 0.01)  # Clamp between 1-10%

    # Train model
    features = data[[
        "data.evt.csm", 
        "hour", 
        "day_of_week", 
        "is_peak_hour", 
        "dwellingid"
    ]]
    
    model = IsolationForest(
        contamination=contamination,
        random_state=42,
        n_estimators=200,
        verbose=0
    )
    
    model.fit(features)
    joblib.dump(model, settings.MODEL_FILE)
    
    return model

def detect_leakage(new_data, model, peak_hours):
    """Integrated leak detection with flood classification using static dwelling ID encoding"""
    if new_data.empty or not model:
        return pd.DataFrame()

    # Store original dwelling IDs for output
    original_dwelling_ids = new_data["dwellingid"].copy()

    # Prepare features with static dwelling ID encoding
    new_data = encode_dwelling_ids_static(new_data)
    new_data["is_peak_hour"] = new_data["hour"].isin(peak_hours).astype(int)
    
    features = new_data[[
        "data.evt.csm", 
        "hour", 
        "day_of_week", 
        "is_peak_hour", 
        "dwellingid"
    ]]

    # 1. Detect anomalies
    predictions = model.predict(features)
    anomalies_mask = (predictions == -1)
    
    # 2. Detect threshold violations (FIXED)
    hourly_max = new_data["hour"].map(max_csm_by_hour)
    daily_max = new_data["date"].map(max_csm_by_day)
    
    threshold_mask = (
        (new_data["data.evt.csm"] > hourly_max * settings.HOURLY_THRESHOLD) |
        (new_data["data.evt.csm"] > daily_max * settings.DAILY_THRESHOLD)
    )
    
    # 3. Flood detection (both conditions)
    flood_mask = anomalies_mask & threshold_mask
    
    # Classify leaks
    new_data["type"] = "normal"
    new_data["reason"] = ""
    
    # Priority 1: Flood leaks
    new_data.loc[flood_mask, "type"] = "flood"
    new_data.loc[flood_mask, "reason"] = "Critical: Combined anomaly and threshold violation"
    
    # Priority 2: Anomalies (non-flood)
    new_data.loc[anomalies_mask & ~flood_mask, "type"] = "anomaly"
    new_data.loc[anomalies_mask & ~flood_mask, "reason"] = "Anomaly detected by model"
    
    # Priority 3: Thresholds (non-flood)
    new_data.loc[threshold_mask & ~flood_mask, "type"] = "threshold" 
    new_data.loc[threshold_mask & ~flood_mask, "reason"] = "Threshold exceeded"

    # Return only leak records with original dwelling IDs
    leaks = new_data[new_data["type"] != "normal"].copy()

    # Restore original dwelling IDs for output
    if not leaks.empty:
        leaks["dwellingid"] = original_dwelling_ids[leaks.index]

    return leaks[["datatime", "dwellingid", "data.evt.csm", "type", "reason"]]

def get_dwelling_id_mapping_info():
    """
    Get information about the current dwelling ID mapping.

    Returns:
        dict: Information about the mapping including total count and examples
    """
    if not _dwelling_id_mapping:
        load_dwelling_id_mapping()

    return {
        "total_mappings": len(_dwelling_id_mapping),
        "mapping_file": DWELLING_ID_MAPPING_FILE,
        "sample_mappings": dict(list(_dwelling_id_mapping.items())[:5]),
        "next_available_number": len(_dwelling_id_mapping) + 1
    }

def reset_dwelling_id_mapping():
    """
    Reset the dwelling ID mapping (use with caution - will affect model consistency).
    """
    global _dwelling_id_mapping, _reverse_mapping

    _dwelling_id_mapping = {}
    _reverse_mapping = {}

    # Remove the mapping file
    if os.path.exists(DWELLING_ID_MAPPING_FILE):
        os.remove(DWELLING_ID_MAPPING_FILE)
        print("🗑️ Dwelling ID mapping reset and file removed")
    else:
        print("🗑️ Dwelling ID mapping reset")

def preload_dwelling_ids_from_data(data):
    """
    Preload dwelling IDs from a dataset to ensure consistent numbering.
    Useful when you want to assign numbers based on frequency or specific order.

    Args:
        data: DataFrame containing dwelling IDs
    """
    if data.empty or 'dwellingid' not in data.columns:
        return

    # Get unique dwelling IDs sorted by frequency (most common first)
    dwelling_id_counts = data['dwellingid'].value_counts()

    print(f"🔄 Preloading {len(dwelling_id_counts)} dwelling IDs...")

    for dwelling_id in dwelling_id_counts.index:
        get_dwelling_id_number(dwelling_id)

    print(f"✅ Preloaded dwelling ID mappings for {len(dwelling_id_counts)} unique dwelling IDs")

def export_dwelling_id_mapping_csv(filename="dwelling_id_mapping.csv"):
    """
    Export dwelling ID mapping to CSV for analysis.

    Args:
        filename: Output CSV filename
    """
    if not _dwelling_id_mapping:
        load_dwelling_id_mapping()

    if not _dwelling_id_mapping:
        print("❌ No dwelling ID mapping to export")
        return

    try:
        import pandas as pd

        mapping_df = pd.DataFrame([
            {"dwelling_id": dwelling_id, "static_number": number}
            for dwelling_id, number in _dwelling_id_mapping.items()
        ])

        mapping_df = mapping_df.sort_values("static_number")
        mapping_df.to_csv(filename, index=False)

        print(f"📊 Exported dwelling ID mapping to {filename}")
        print(f"   Total mappings: {len(mapping_df)}")

    except Exception as e:
        print(f"❌ Error exporting mapping: {e}")