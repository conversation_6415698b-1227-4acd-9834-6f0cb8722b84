#!/usr/bin/env python3
"""
Test script to demonstrate static dwelling ID mapping for model training.
This shows how dwelling IDs get consistent static numbers instead of random categorical codes.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from app.utils.anomaly_detection import (
        get_dwelling_id_number,
        encode_dwelling_ids_static,
        get_dwelling_id_mapping_info,
        reset_dwelling_id_mapping,
        preload_dwelling_ids_from_data,
        export_dwelling_id_mapping_csv,
        load_dwelling_id_mapping,
        train_anomaly_model,
        detect_leakage
    )
    print("✅ Successfully imported static dwelling ID functions")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def create_sample_data():
    """Create sample data with dwelling IDs for testing."""
    
    # Sample dwelling IDs (mix of UUIDs and simple IDs)
    dwelling_ids = [
        "2cee70f7-339f-4750-9bf3-eb4fede8f672",
        "09442bb9-d815-4dd8-844e-9914d13dcdb7",
        "0e1d10a5-3e1d-4c25-9a5c-ef223dc60ad3",
        "dwelling_001",
        "dwelling_002",
        "house_alpha",
        "house_beta",
        "apt_123",
        "apt_456"
    ]
    
    # Create sample data
    np.random.seed(42)  # For reproducible results
    
    data_list = []
    base_time = datetime.now() - timedelta(days=7)
    
    for i in range(1000):  # 1000 sample records
        dwelling_id = np.random.choice(dwelling_ids)
        timestamp = base_time + timedelta(hours=i * 0.1)
        
        # Simulate water consumption data
        hour = timestamp.hour
        day_of_week = timestamp.weekday()
        
        # Base consumption with some patterns
        base_consumption = np.random.normal(5.0, 2.0)  # Base 5L/min
        if 6 <= hour <= 9 or 18 <= hour <= 22:  # Peak hours
            base_consumption *= 1.5
        
        consumption = max(0, base_consumption + np.random.normal(0, 0.5))
        
        data_list.append({
            'datatime': timestamp,
            'dwellingid': dwelling_id,
            'data.evt.csm': consumption,
            'hour': hour,
            'day_of_week': day_of_week,
            'date': timestamp.date()
        })
    
    return pd.DataFrame(data_list)

def test_static_vs_categorical_encoding():
    """Compare static encoding vs categorical encoding."""
    print("\n🔬 Testing Static vs Categorical Encoding")
    print("=" * 60)
    
    # Create sample data
    sample_data = create_sample_data()
    print(f"Created sample data with {len(sample_data)} records")
    print(f"Unique dwelling IDs: {sample_data['dwellingid'].nunique()}")
    
    # Test 1: Categorical encoding (current problematic approach)
    print("\n1️⃣ Categorical Encoding (Current - Problematic):")
    data_cat = sample_data.copy()
    data_cat["dwellingid_cat"] = data_cat["dwellingid"].astype("category").cat.codes
    
    print("First encoding:")
    cat_mapping_1 = dict(zip(data_cat["dwellingid"], data_cat["dwellingid_cat"]))
    for dwelling_id, code in list(cat_mapping_1.items())[:5]:
        print(f"  {dwelling_id[:30]:<30} → {code}")
    
    # Simulate second training session (different order)
    data_cat_2 = sample_data.sample(frac=1).reset_index(drop=True)  # Shuffle
    data_cat_2["dwellingid_cat"] = data_cat_2["dwellingid"].astype("category").cat.codes
    
    print("\nSecond encoding (after shuffle - INCONSISTENT!):")
    cat_mapping_2 = dict(zip(data_cat_2["dwellingid"], data_cat_2["dwellingid_cat"]))
    for dwelling_id, code in list(cat_mapping_2.items())[:5]:
        print(f"  {dwelling_id[:30]:<30} → {code}")
    
    # Show inconsistency
    print("\n❌ Inconsistency in categorical encoding:")
    for dwelling_id in list(cat_mapping_1.keys())[:3]:
        code1 = cat_mapping_1[dwelling_id]
        code2 = cat_mapping_2[dwelling_id]
        consistent = "✅" if code1 == code2 else "❌"
        print(f"  {dwelling_id[:30]:<30}: {code1} → {code2} {consistent}")
    
    # Test 2: Static encoding (new improved approach)
    print("\n2️⃣ Static Encoding (New - Consistent):")
    
    # Reset mapping for clean test
    reset_dwelling_id_mapping()
    
    # First encoding
    data_static_1 = encode_dwelling_ids_static(sample_data.copy())
    static_mapping_1 = dict(zip(sample_data["dwellingid"], data_static_1["dwellingid"]))
    
    print("First encoding:")
    for dwelling_id, code in list(static_mapping_1.items())[:5]:
        print(f"  {dwelling_id[:30]:<30} → {code}")
    
    # Second encoding (same data, different order)
    data_shuffled = sample_data.sample(frac=1).reset_index(drop=True)
    data_static_2 = encode_dwelling_ids_static(data_shuffled)
    static_mapping_2 = dict(zip(data_shuffled["dwellingid"], data_static_2["dwellingid"]))
    
    print("\nSecond encoding (after shuffle - CONSISTENT!):")
    for dwelling_id, code in list(static_mapping_2.items())[:5]:
        print(f"  {dwelling_id[:30]:<30} → {code}")
    
    # Show consistency
    print("\n✅ Consistency in static encoding:")
    for dwelling_id in list(static_mapping_1.keys())[:3]:
        code1 = static_mapping_1[dwelling_id]
        code2 = static_mapping_2[dwelling_id]
        consistent = "✅" if code1 == code2 else "❌"
        print(f"  {dwelling_id[:30]:<30}: {code1} → {code2} {consistent}")

def test_mapping_persistence():
    """Test that mappings persist across sessions."""
    print("\n💾 Testing Mapping Persistence")
    print("=" * 40)
    
    # Reset for clean test
    reset_dwelling_id_mapping()
    
    # Create some mappings
    test_ids = ["dwelling_A", "dwelling_B", "dwelling_C"]
    
    print("Creating initial mappings:")
    for dwelling_id in test_ids:
        number = get_dwelling_id_number(dwelling_id)
        print(f"  {dwelling_id} → {number}")
    
    # Show mapping info
    info = get_dwelling_id_mapping_info()
    print(f"\nMapping info: {info['total_mappings']} total mappings")
    
    # Simulate restart by reloading
    print("\nSimulating application restart...")
    load_dwelling_id_mapping()
    
    print("Mappings after reload:")
    for dwelling_id in test_ids:
        number = get_dwelling_id_number(dwelling_id)
        print(f"  {dwelling_id} → {number}")
    
    # Add new dwelling ID
    new_id = "dwelling_D"
    new_number = get_dwelling_id_number(new_id)
    print(f"\nAdding new dwelling ID: {new_id} → {new_number}")

def test_model_training_with_static_ids():
    """Test model training with static dwelling ID encoding."""
    print("\n🤖 Testing Model Training with Static IDs")
    print("=" * 50)
    
    # Create training data
    training_data = create_sample_data()
    peak_hours = [7, 8, 9, 18, 19, 20, 21]
    
    print(f"Training data: {len(training_data)} records")
    print(f"Unique dwelling IDs: {training_data['dwellingid'].nunique()}")
    
    # Preload dwelling IDs for consistent ordering
    print("\nPreloading dwelling IDs...")
    preload_dwelling_ids_from_data(training_data)
    
    # Show mapping before training
    info = get_dwelling_id_mapping_info()
    print(f"Dwelling ID mappings: {info['total_mappings']}")
    print("Sample mappings:")
    for dwelling_id, number in list(info['sample_mappings'].items()):
        print(f"  {dwelling_id[:30]:<30} → {number}")
    
    # Train model
    print("\nTraining model with static dwelling ID encoding...")
    try:
        model = train_anomaly_model(training_data.copy(), peak_hours)
        if model:
            print("✅ Model training successful!")
        else:
            print("❌ Model training failed!")
    except Exception as e:
        print(f"❌ Model training error: {e}")
    
    # Test detection with new data
    print("\nTesting leak detection with static encoding...")
    test_data = training_data.sample(10).copy()  # Sample some data for testing
    
    try:
        if model:
            leaks = detect_leakage(test_data, model, peak_hours)
            print(f"Detected {len(leaks)} potential leaks")
            if not leaks.empty:
                print("Sample leak detection results:")
                for _, leak in leaks.head(3).iterrows():
                    print(f"  {leak['dwellingid']}: {leak['type']} - {leak['reason']}")
        else:
            print("❌ Cannot test detection without trained model")
    except Exception as e:
        print(f"❌ Leak detection error: {e}")

def test_export_functionality():
    """Test exporting dwelling ID mappings."""
    print("\n📊 Testing Export Functionality")
    print("=" * 35)
    
    try:
        export_dwelling_id_mapping_csv("test_dwelling_mapping.csv")
        
        # Check if file was created
        if os.path.exists("test_dwelling_mapping.csv"):
            print("✅ CSV export successful!")
            
            # Read and show first few lines
            import pandas as pd
            df = pd.read_csv("test_dwelling_mapping.csv")
            print(f"Exported {len(df)} mappings")
            print("First 5 mappings:")
            print(df.head().to_string(index=False))
        else:
            print("❌ CSV file not created")
            
    except Exception as e:
        print(f"❌ Export error: {e}")

def main():
    """Main test function."""
    print("🚀 Static Dwelling ID Mapping Test Suite")
    print("=" * 60)
    
    # Test static vs categorical encoding
    test_static_vs_categorical_encoding()
    
    # Test mapping persistence
    test_mapping_persistence()
    
    # Test model training
    test_model_training_with_static_ids()
    
    # Test export functionality
    test_export_functionality()
    
    print("\n✅ Static dwelling ID mapping test suite completed!")
    print("\n📋 Benefits of Static Mapping:")
    print("- ✅ Consistent dwelling ID numbers across training sessions")
    print("- ✅ Model can handle new dwelling IDs gracefully")
    print("- ✅ Mappings persist between application restarts")
    print("- ✅ Predictable and debuggable ID assignments")
    print("- ✅ Better model stability and performance")

if __name__ == "__main__":
    main()
