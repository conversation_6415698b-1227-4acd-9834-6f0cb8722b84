# Static Dwelling ID Mapping for Model Training

## Problem with Current Approach

The current categorical encoding approach has serious issues:

```python
# PROBLEMATIC: Inconsistent encoding
new_data["dwellingid"] = new_data["dwellingid"].astype("category").cat.codes
```

### Issues:
1. **Inconsistent Numbers**: Same dwelling ID gets different numbers in different training sessions
2. **Order Dependency**: Numbers depend on the order data is processed
3. **Model Instability**: Model performance varies between training sessions
4. **New ID Problems**: Model can't handle dwelling IDs not seen during training

### Example of the Problem:
```
Training Session 1:
  dwelling_A → 0
  dwelling_B → 1
  dwelling_C → 2

Training Session 2 (different order):
  dwelling_B → 0  ❌ Changed!
  dwelling_C → 1  ❌ Changed!
  dwelling_A → 2  ❌ Changed!
```

## Solution: Static Dwelling ID Mapping

The new approach assigns **permanent, consistent numbers** to each dwelling ID:

```python
# NEW: Consistent static encoding
new_data = encode_dwelling_ids_static(new_data)
```

### Benefits:
1. **✅ Consistent Numbers**: Same dwelling ID always gets the same number
2. **✅ Order Independent**: Numbers don't change based on data order
3. **✅ Model Stability**: Consistent training results
4. **✅ Persistent Mapping**: Numbers saved to file and reloaded
5. **✅ New ID Handling**: New dwelling IDs get next available number

### Example of the Solution:
```
Training Session 1:
  dwelling_A → 1
  dwelling_B → 2
  dwelling_C → 3

Training Session 2 (any order):
  dwelling_A → 1  ✅ Consistent!
  dwelling_B → 2  ✅ Consistent!
  dwelling_C → 3  ✅ Consistent!
  dwelling_D → 4  ✅ New ID gets next number!
```

## Implementation Details

### Core Functions

1. **`get_dwelling_id_number(dwelling_id)`**
   - Returns static number for dwelling ID
   - Creates new mapping if ID not seen before
   - Saves mapping to persistent file

2. **`encode_dwelling_ids_static(data)`**
   - Replaces dwelling IDs with static numbers
   - Uses consistent mapping across sessions
   - Handles new IDs gracefully

3. **`load_dwelling_id_mapping()`** / **`save_dwelling_id_mapping()`**
   - Persists mappings between application restarts
   - Stores in JSON format for easy inspection

### Updated Training Function

```python
def train_anomaly_model(data, peak_hours):
    """Train model with static dwelling ID encoding"""
    if data.empty:
        return None

    print("🏠 Encoding dwelling IDs with static mapping...")
    
    # Use static encoding instead of categorical
    data = encode_dwelling_ids_static(data)
    data["is_peak_hour"] = data["hour"].isin(peak_hours).astype(int)
    
    # Rest of training logic...
```

### Updated Detection Function

```python
def detect_leakage(new_data, model, peak_hours):
    """Detect leaks with static dwelling ID encoding"""
    if new_data.empty or not model:
        return pd.DataFrame()

    # Store original IDs for output
    original_dwelling_ids = new_data["dwellingid"].copy()
    
    # Use static encoding for model prediction
    new_data = encode_dwelling_ids_static(new_data)
    new_data["is_peak_hour"] = new_data["hour"].isin(peak_hours).astype(int)
    
    # Model prediction logic...
    
    # Restore original IDs in output
    leaks["dwellingid"] = original_dwelling_ids[leaks.index]
    return leaks
```

## File Structure

### `dwelling_id_mapping.json`
```json
{
  "2cee70f7-339f-4750-9bf3-eb4fede8f672": 1,
  "09442bb9-d815-4dd8-844e-9914d13dcdb7": 2,
  "dwelling_001": 3,
  "dwelling_002": 4
}
```

### Exported CSV for Analysis
```csv
dwelling_id,static_number
2cee70f7-339f-4750-9bf3-eb4fede8f672,1
09442bb9-d815-4dd8-844e-9914d13dcdb7,2
dwelling_001,3
dwelling_002,4
```

## Usage Examples

### Basic Usage
```python
from app.utils.anomaly_detection import get_dwelling_id_number, encode_dwelling_ids_static

# Get static number for dwelling ID
number = get_dwelling_id_number("dwelling_123")
print(f"dwelling_123 → {number}")  # Always same number

# Encode entire dataset
data_encoded = encode_dwelling_ids_static(data)
```

### Training with Static IDs
```python
# Load your training data
training_data = load_training_data()

# Optionally preload IDs for consistent ordering
preload_dwelling_ids_from_data(training_data)

# Train model (now uses static encoding automatically)
model = train_anomaly_model(training_data, peak_hours)
```

### Monitoring and Management
```python
# Get mapping information
info = get_dwelling_id_mapping_info()
print(f"Total mappings: {info['total_mappings']}")

# Export for analysis
export_dwelling_id_mapping_csv("dwelling_mappings.csv")

# Reset if needed (use with caution!)
reset_dwelling_id_mapping()
```

## Migration Guide

### Step 1: Update Imports
```python
from app.utils.anomaly_detection import (
    encode_dwelling_ids_static,
    get_dwelling_id_mapping_info,
    preload_dwelling_ids_from_data
)
```

### Step 2: Replace Categorical Encoding
```python
# OLD (remove this):
data["dwellingid"] = data["dwellingid"].astype("category").cat.codes

# NEW (use this):
data = encode_dwelling_ids_static(data)
```

### Step 3: Update Training Pipeline
- The `train_anomaly_model()` function is already updated
- The `detect_leakage()` function is already updated
- No changes needed to calling code

### Step 4: Optional Preloading
```python
# For consistent numbering based on frequency
preload_dwelling_ids_from_data(historical_data)
```

## Benefits Summary

| Aspect | Categorical Encoding | Static Mapping |
|--------|---------------------|----------------|
| **Consistency** | ❌ Changes between sessions | ✅ Always same numbers |
| **Persistence** | ❌ Lost on restart | ✅ Saved to file |
| **New IDs** | ❌ Breaks model | ✅ Handles gracefully |
| **Debugging** | ❌ Hard to trace | ✅ Easy to inspect |
| **Model Stability** | ❌ Varies | ✅ Consistent |
| **Performance** | ❌ Unpredictable | ✅ Stable |

## Testing

Run the test suite to see the difference:
```bash
python test_static_dwelling_ids.py
```

The test demonstrates:
- Inconsistency of categorical encoding
- Consistency of static mapping
- Persistence across sessions
- Model training improvements
- Export functionality

## Monitoring

### Check Mapping Status
```python
info = get_dwelling_id_mapping_info()
print(f"Total dwelling IDs mapped: {info['total_mappings']}")
print(f"Next available number: {info['next_available_number']}")
```

### Export for Analysis
```python
export_dwelling_id_mapping_csv("analysis/dwelling_mappings.csv")
```

### View Mapping File
```bash
cat dwelling_id_mapping.json
```

This static mapping approach ensures **consistent, reliable model training** while maintaining **full compatibility** with your existing leak detection system!
