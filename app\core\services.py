import time
from datetime import datetime, timedelta
from threading import Thread, Lock
import pandas as pd
from collections import defaultdict
from app.db.mongodb import get_db, fetch_data_with_utf8_ids, fetch_all_dwelling_ids, decode_utf8_safely, decode_dwelling_id_advanced
from app.utils.preprocess import preprocess_data
from app.utils.anomaly_detection import train_anomaly_model, detect_leakage
from app.utils.visualization import identify_peak_hours
from app.utils.thresholds import update_max_csm
from app.core.config import settings

class MonitoringService:
    def __init__(self):
        self.is_monitoring = False
        self.thread = None
        self.model = None
        self.peak_hours = []
        self.leak_events = []  # Store all leak events in memory
        self.leak_lock = Lock()  # Thread-safe lock for leak events
        self.forgotten_tap_timers = defaultdict(float)  # {dwellingid: start_timestamp}
        self.consumption_history = defaultdict(list)  # {dwellingid: [(timestamp, csm)]}
        self.dwelling_profiles = defaultdict(dict)

    def start(self):
        """Start the monitoring service."""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.thread = Thread(target=self._monitor)
            self.thread.start()

    def stop(self):
        """Stop the monitoring service."""
        self.is_monitoring = False
        if self.thread:
            self.thread.join()

    def _initialize_model(self, collection):
        """Initialize the anomaly detection model with historical data."""
        print("🔨 Initializing model with historical data...")
        max_retries = 3
        retry_delay = 500  # seconds
        
        for attempt in range(max_retries):
            try:
                # Fetch historical data (exclude _id)
                historical_data = pd.DataFrame(list(collection.find(
                    {"datatime": {"$gte": datetime.utcnow() - timedelta(days=settings.WINDOW_SIZE)}},
                    {"_id": 0}  # Exclude MongoDB _id
                )))

                # Preprocess data
                processed_data = preprocess_data(historical_data)
                
                if processed_data.empty:
                    print("⚠️ No valid data after preprocessing")
                    raise ValueError("Empty DataFrame after preprocessing")
                    
                # Train model
                self.peak_hours = identify_peak_hours(processed_data)
                self.model = train_anomaly_model(processed_data, self.peak_hours)
                update_max_csm(processed_data)
                print(f"✅ Model initialized with {len(processed_data)} records")
                return

            except Exception as e:
                if attempt == max_retries - 1:
                    print(f"🚨 Failed to initialize model after {max_retries} attempts: {str(e)}")
                    raise
                print(f"⚠️ Model initialization failed (attempt {attempt+1}), retrying...")
                time.sleep(retry_delay * (attempt + 1))

    def _monitor(self):
        """Main monitoring loop."""
        try:
            db = get_db()
            collection = db["raw_data_ts"] # type: ignore
        except Exception as e:
            print(f"🚨 Critical error: Failed to connect to MongoDB: {str(e)}")
            self.is_monitoring = False
            return

        # Initialize with historical data
        try:
            self._initialize_model(collection)
        except Exception as e:
            print(f"🚨 Critical error: Failed to initialize model: {str(e)}")
            self.is_monitoring = False
            return

        last_checked = None
        last_retrained = time.time()

        while self.is_monitoring:
            try:
                current_time = datetime.utcnow()
                min_time = current_time - timedelta(minutes=3)

                # Build proper MongoDB query
                query = {}
                if last_checked:
                    query["datatime"] = {"$gt": last_checked}
                else:
                    query["datatime"] = {"$gt": min_time}

                # Fetch new data with UTF-8 decoded IDs
                new_data = fetch_data_with_utf8_ids(collection, query, {"_id": 0})
                
                if not new_data.empty:
                    processed_data = preprocess_data(new_data)
                    
                    if not processed_data.empty:
                        print(f"\n📥 Received {len(processed_data)} new readings")
                        update_max_csm(processed_data)

                        # Detect leaks using anomaly detection
                        leak_events = detect_leakage(processed_data, self.model, self.peak_hours)

                        # Detect forgotten taps and tap leakage
                        self._check_forgotten_taps(processed_data)

                        # Store all leaks in memory
                        if not leak_events.empty:
                            with self.leak_lock:
                                self.leak_events.extend(leak_events.to_dict("records"))

                        # Print real-time updates
                        for _, row in processed_data.iterrows():
                            hour = row["hour"]
                            csm = row["data.evt.csm"]
                            dt = row["datatime"]
                            cid = row["dwellingid"]
                            peak_status = "PEAK" if hour in self.peak_hours else "off-peak"
                            print(f"[REALTIME] {dt} | cid:{cid} | CSM: {csm:.2f} | Hour: {hour:02d}:00 ({peak_status})")

                        last_checked = processed_data["datatime"].max()

                # Retrain model periodically
                if time.time() - last_retrained > settings.RETRAIN_INTERVAL:
                    print("\n🔄 Retraining model...")
                    self._initialize_model(collection)
                    last_retrained = time.time()

                time.sleep(settings.MONITOR_INTERVAL)

            except Exception as e:
                print(f"⚠️ Monitoring error: {str(e)}")
                time.sleep(30)  # Wait before retrying
                
    def update_profiles(self, data):
        """Update normal usage patterns per dwelling"""
        for dwelling_id, group in data.groupby('dwellingid'):
            # Hourly baselines
            hourly_avg = group.groupby('hour')['data.evt.csm'].mean()
            self.dwelling_profiles[dwelling_id]['hourly_avg'] = hourly_avg

            # Daily usage patterns
            daily_usage = group.groupby('date')['data.evt.csm'].sum().mean()
            self.dwelling_profiles[dwelling_id]['daily_avg'] = daily_usage
            
    def _check_forgotten_taps(self, data):
        """Detect continuous low flow (forgotten tap or tap leakage)."""
        current_time = time.time()

        for _, row in data.iterrows():
            dwellingid = row["dwellingid"]
            csm = row["data.evt.csm"]  # Should be in L/min

            # Check for tap leakage
            tap_leak_alert = self._check_tap_leakage(dwellingid, csm, row["datatime"])
            if tap_leak_alert:
                print(f"🚰 Tap Leakage Detected at dwelling {dwellingid}:")
                print(f"  - Average Consumption: {tap_leak_alert['average_consumption']:.2f} L/min")
                print(f"  - Duration: {tap_leak_alert['duration']}")
                print(f"  - Reason: {tap_leak_alert['reason']}")
                with self.leak_lock:
                    self.leak_events.append(tap_leak_alert)
                    
    def _check_tap_leakage(self, dwelling_id, csm, timestamp):
        """Detect continuous low-level consumption (tap leakage) based on a threshold."""
        alert = None
        profile = self.dwelling_profiles.get(dwelling_id)

        if profile and 'hourly_avg' in profile:
            current_hour = timestamp.hour
            baseline = profile['hourly_avg'].get(current_hour, 0)
            threshold = baseline * (1 + settings.TAP_LEAKAGE_THRESHOLD)

            # Maintain consumption history for the last 6 readings (1 hour if 10-minute intervals)
            self.consumption_history[dwelling_id].append((timestamp, csm))
            self.consumption_history[dwelling_id] = [
                (ts, val) for ts, val in self.consumption_history[dwelling_id]
                if ts > timestamp - timedelta(hours=1)  # Keep only the last hour of data
            ]

            # Check for tap leakage
            if csm > 0.00:  # Ignore zero consumption
                recent_readings = [val for ts, val in self.consumption_history[dwelling_id]]
                if len(recent_readings) >= 6:  # Ensure we have enough data
                    last_six_readings = recent_readings[-6:]
                    avg_consumption = sum(last_six_readings) / len(last_six_readings)

                    # Check if all readings are within a small range of the average
                    if all(abs(val - avg_consumption) <= settings.TAP_LEAKAGE_VARIANCE for val in last_six_readings): # type: ignore
                        # Check if the average is above the baseline but below the threshold
                        if baseline < avg_consumption <= threshold:
                            alert = {
                                'type': 'tap_leakage',
                                'start_time': timestamp - timedelta(minutes=30),  # Assuming 10-minute intervals
                                'duration': timedelta(minutes=30),
                                'average_consumption': avg_consumption,
                                'reason': f"Sustained low flow ({avg_consumption:.2f} L/min) for 30 minutes"
                            }
                            return alert

        return alert

    def _record_forgotten_tap_leak(self, row, duration):
        """Record a forgotten tap leak event."""
        leak_event = {
            "datatime": row["datatime"],
            "dwellingid": row["dwellingid"],
            "csm": row["data.evt.csm"],
            "duration": f"{duration:.1f} minutes",
            "type": "forgotten_tap",
            "reason": f"Continuous low flow ({row['data.evt.csm']} L/min) for {duration:.1f} minutes"
        }
        with self.leak_lock:
            self.leak_events.append(leak_event)

# Singleton instance of the monitoring service
monitoring_service = MonitoringService()

def start_monitoring():
    """Start the monitoring service."""
    print("\n🚀 Monitoring started at", datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"))
    print("📈 Identified peak hours:", sorted(monitoring_service.peak_hours))
    print("⚖️ Active thresholds: Hourly", settings.HOURLY_THRESHOLD, "x | Daily", settings.DAILY_THRESHOLD, "x")
    print("\n🔍 Starting real-time monitoring...\n")
    monitoring_service.start()

def stop_monitoring():
    """Stop the monitoring service."""
    monitoring_service.stop()

def get_leak_events() -> list:
    """Retrieve all leak events from memory."""
    try:
        with monitoring_service.leak_lock:
            return monitoring_service.leak_events.copy()
    except Exception as e:
        print(f"⚠️ Error retrieving leaks: {str(e)}")
        return []

def get_all_dwelling_ids_utf8() -> list:
    """
    Fetch all dwelling IDs with proper UTF-8 decoding.

    Returns:
        List of UTF-8 decoded dwelling IDs
    """
    try:
        db = get_db()
        collection = db["raw_data_ts"] # type: ignore
        return fetch_all_dwelling_ids(collection)
    except Exception as e:
        print(f"❌ Error fetching UTF-8 dwelling IDs: {str(e)}")
        return []

def get_dwelling_data_utf8(dwelling_id: str, start_time=None, end_time=None) -> pd.DataFrame:
    """
    Fetch data for a specific dwelling with UTF-8 decoded IDs.

    Args:
        dwelling_id: The dwelling ID to fetch data for
        start_time: Optional start time filter
        end_time: Optional end time filter

    Returns:
        DataFrame with UTF-8 decoded data
    """
    try:
        db = get_db()
        collection = db["raw_data_ts"] # type: ignore

        # Build query with properly decoded dwelling ID
        query: dict = {"dwellingid": decode_dwelling_id_advanced(dwelling_id)}

        if start_time:
            if "datatime" not in query:
                query["datatime"] = {}
            query["datatime"]["$gte"] = start_time
        if end_time:
            if "datatime" not in query:
                query["datatime"] = {}
            query["datatime"]["$lte"] = end_time

        return fetch_data_with_utf8_ids(collection, query, {"_id": 0})

    except Exception as e:
        print(f"❌ Error fetching UTF-8 data for dwelling {dwelling_id}: {str(e)}")
        return pd.DataFrame()

def process_dwelling_id_safely(dwelling_id) -> str:
    """
    Process a dwelling ID to ensure it's properly decoded from any format.

    Args:
        dwelling_id: Raw dwelling ID from database or input

    Returns:
        Properly decoded dwelling ID string
    """
    return decode_dwelling_id_advanced(dwelling_id)

def get_dwelling_id_statistics_utf8() -> dict:
    """
    Get statistics about dwelling IDs with UTF-8 decoding.

    Returns:
        Dictionary with dwelling ID statistics
    """
    try:
        db = get_db()
        collection = db["raw_data_ts"] # type: ignore

        # Get all dwelling IDs with UTF-8 decoding
        dwelling_ids = fetch_all_dwelling_ids(collection)

        # Calculate statistics
        stats = {
            "total_dwellings": len(dwelling_ids),
            "dwelling_ids": dwelling_ids,
            "encoding_info": {
                "encoding": "UTF-8",
                "safe_decoding": True,
                "error_handling": "replace"
            }
        }

        # Check for any potential encoding issues
        problematic_ids = []
        for dwelling_id in dwelling_ids:
            try:
                # Test if the ID can be properly encoded/decoded
                dwelling_id.encode('utf-8').decode('utf-8')
            except (UnicodeEncodeError, UnicodeDecodeError):
                problematic_ids.append(dwelling_id)

        stats["encoding_info"]["problematic_ids"] = problematic_ids
        stats["encoding_info"]["clean_ids_count"] = len(dwelling_ids) - len(problematic_ids)

        print(f"✅ UTF-8 dwelling ID statistics: {stats['total_dwellings']} total, {stats['encoding_info']['clean_ids_count']} clean")

        return stats

    except Exception as e:
        print(f"❌ Error getting UTF-8 dwelling ID statistics: {str(e)}")
        return {
            "total_dwellings": 0,
            "dwelling_ids": [],
            "encoding_info": {"error": str(e)}
        }