# UTF-8 ID Fetching and Decoding in AQUESA Leak Detection System

## Overview

This document describes the enhanced UTF-8 encoding and decoding functionality added to the AQUESA Smart Water Leak Detection System. The system now properly handles all dwelling IDs and other identifiers with full UTF-8 support, ensuring compatibility with international characters, emojis, and various encoding formats.

## Key Features

### 🔧 Core UTF-8 Functions

1. **Safe UTF-8 Decoding**: `decode_utf8_safely(value)`
   - Handles strings, bytes, integers, and None values
   - Uses 'replace' error handling to prevent crashes
   - Ensures all output is valid UTF-8

2. **Database Connection with UTF-8 Support**
   - MongoDB connection configured with UTF-8 codec options
   - Strict Unicode decode error handling
   - Proper document class configuration

3. **Enhanced Data Fetching**
   - `fetch_all_dwelling_ids()`: Retrieves all unique dwelling IDs with UTF-8 decoding
   - `fetch_data_with_utf8_ids()`: Fetches data with UTF-8 decoded ID fields
   - `get_dwelling_id_mapping()`: Creates mapping of original to decoded IDs

### 🏠 Dwelling ID Management

#### New Service Functions

1. **`get_all_dwelling_ids_utf8()`**
   - Fetches all dwelling IDs from the database
   - Returns list of UTF-8 decoded dwelling IDs
   - Includes error handling and logging

2. **`get_dwelling_data_utf8(dwelling_id, start_time, end_time)`**
   - Retrieves data for specific dwelling with UTF-8 safety
   - Supports time range filtering
   - Returns pandas DataFrame with decoded IDs

3. **`process_dwelling_id_safely(dwelling_id)`**
   - Processes any dwelling ID input to ensure UTF-8 encoding
   - Handles various input types (string, bytes, int, None)
   - Returns clean UTF-8 string

4. **`get_dwelling_id_statistics_utf8()`**
   - Provides comprehensive statistics about dwelling IDs
   - Includes encoding information and problem detection
   - Reports clean vs problematic IDs

### 🌐 API Endpoints

#### New UTF-8 Enabled Endpoints

1. **`GET /api/dwelling-ids`**
   ```json
   {
     "dwelling_ids": ["DW001", "DW002", "café_dwelling_003"],
     "count": 3,
     "encoding": "UTF-8"
   }
   ```

2. **`GET /api/dwelling-ids/statistics`**
   ```json
   {
     "total_dwellings": 150,
     "dwelling_ids": ["..."],
     "encoding_info": {
       "encoding": "UTF-8",
       "safe_decoding": true,
       "error_handling": "replace",
       "clean_ids_count": 148,
       "problematic_ids": ["id1", "id2"]
     }
   }
   ```

3. **`GET /api/dwelling/{dwelling_id}/data`**
   ```json
   {
     "dwelling_id": "café_dwelling_001",
     "original_dwelling_id": "café_dwelling_001",
     "data": [...],
     "count": 25,
     "encoding": "UTF-8"
   }
   ```

## Implementation Details

### Database Configuration

```python
# Enhanced MongoDB connection with UTF-8 support
codec_options = CodecOptions(
    unicode_decode_error_handler='strict',
    document_class=dict
)
db = client.get_database(settings.DB_NAME, codec_options=codec_options)
```

### UTF-8 Safe Decoding

```python
def decode_utf8_safely(value: Any) -> str:
    """Safely decode any value to UTF-8 string format."""
    if value is None:
        return ""
    
    if isinstance(value, bytes):
        return value.decode('utf-8', errors='replace')
    
    if isinstance(value, str):
        # Ensure proper UTF-8 encoding
        return value.encode('utf-8', errors='replace').decode('utf-8', errors='replace')
    
    # Convert other types to string and ensure UTF-8
    str_value = str(value)
    return str_value.encode('utf-8', errors='replace').decode('utf-8', errors='replace')
```

### Enhanced Data Processing

The monitoring service now uses UTF-8 safe data fetching:

```python
# Old approach
new_data = pd.DataFrame(list(collection.find(query, {"_id": 0})))

# New UTF-8 safe approach
new_data = fetch_data_with_utf8_ids(collection, query, {"_id": 0})
```

## Usage Examples

### Basic Usage

```python
from app.core.services import get_all_dwelling_ids_utf8, process_dwelling_id_safely

# Get all dwelling IDs with UTF-8 decoding
dwelling_ids = get_all_dwelling_ids_utf8()
print(f"Found {len(dwelling_ids)} dwelling IDs")

# Process a dwelling ID safely
safe_id = process_dwelling_id_safely("café_dwelling_🏠")
print(f"Processed ID: {safe_id}")
```

### API Usage

```bash
# Get all dwelling IDs
curl http://localhost:8000/api/dwelling-ids

# Get dwelling ID statistics
curl http://localhost:8000/api/dwelling-ids/statistics

# Get data for specific dwelling
curl http://localhost:8000/api/dwelling/DW001/data
```

### Testing

Run the test suite to verify UTF-8 functionality:

```bash
python test_utf8_ids.py
```

## Benefits

1. **International Support**: Handles dwelling IDs with international characters
2. **Emoji Support**: Supports emojis and special Unicode characters in IDs
3. **Error Prevention**: Prevents Unicode encoding/decoding errors
4. **Data Integrity**: Ensures all IDs are consistently encoded
5. **Backward Compatibility**: Works with existing ASCII IDs
6. **Comprehensive Logging**: Provides detailed feedback on encoding operations

## Error Handling

The system uses 'replace' error handling strategy:
- Invalid UTF-8 sequences are replaced with replacement characters
- No crashes due to encoding issues
- Comprehensive logging of encoding problems
- Statistics tracking of problematic IDs

## Performance Considerations

- UTF-8 decoding adds minimal overhead
- Database queries are optimized with aggregation pipelines
- Caching of dwelling ID mappings for efficiency
- Batch processing for large datasets

## Migration Notes

Existing systems will automatically benefit from UTF-8 support:
- No database schema changes required
- Existing ASCII IDs work unchanged
- New UTF-8 IDs are handled transparently
- API responses include encoding information

## Troubleshooting

### Common Issues

1. **Connection Issues**: Check MongoDB codec options configuration
2. **Encoding Errors**: Review UTF-8 decoding function logs
3. **Performance**: Monitor dwelling ID statistics for problematic IDs

### Debug Information

Use the statistics endpoint to get detailed encoding information:
```python
stats = get_dwelling_id_statistics_utf8()
print(f"Clean IDs: {stats['encoding_info']['clean_ids_count']}")
print(f"Problematic IDs: {stats['encoding_info']['problematic_ids']}")
```

## Future Enhancements

- Support for additional character encodings
- Automatic encoding detection
- Performance optimizations for large datasets
- Enhanced error reporting and recovery
