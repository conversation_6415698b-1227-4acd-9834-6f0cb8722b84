# AQUESA Smart Water Leak Detection System - Frontend

A modern, responsive web dashboard for monitoring and managing the AQUESA leak detection system.

## Features

### 🏠 **Real-time Monitoring Dashboard**
- Live leak detection alerts
- System status monitoring
- Auto-refresh capabilities
- Sound alerts for new leaks

### 📊 **Comprehensive Analytics**
- Leak detection trends
- Leak type distribution charts
- Dwelling ID statistics
- System health monitoring

### 🔧 **Dwelling Management**
- UTF-8 dwelling ID display
- Search and filter capabilities
- Individual dwelling details
- Encoding status monitoring

### ⚙️ **System Control**
- Start/stop monitoring
- Real-time status updates
- Configurable settings
- API endpoint management

## Quick Start

### 1. Start the AQUESA API Server
```bash
# From the main project directory
cd d:\elementure\aquesa_leak_detection
uvicorn app.main:app --reload --port 8000
```

### 2. Start the Frontend Server
```bash
# From the frontend directory
cd frontend
python server.py
```

### 3. Open in Browser
Navigate to: `http://localhost:3000`

## File Structure

```
frontend/
├── index.html          # Main dashboard HTML
├── styles.css          # Custom CSS styles
├── app.js             # Main JavaScript application
├── server.py          # Simple HTTP server
└── README.md          # This file
```

## Dashboard Sections

### 🎛️ **Control Panel**
- **Start/Stop Monitoring**: Control the leak detection system
- **Status Indicator**: Real-time connection status
- **Refresh Button**: Manual data refresh

### 📈 **Statistics Cards**
- **Total Dwellings**: Number of monitored dwelling units
- **Active Leaks**: Current leak alerts
- **Flood Alerts**: Critical flood-type leaks
- **Clean IDs**: UTF-8 encoded dwelling IDs

### 📋 **Leak Detection Tab**
- Real-time leak event table
- Leak type classification (Normal, Tap Leakage, Flood)
- Consumption values and timestamps
- Quick action buttons for dwelling details

### 🏘️ **Dwelling Management Tab**
- Complete dwelling ID listing
- UTF-8 encoding status
- Search and filter functionality
- Individual dwelling data access

### 📊 **Analytics Tab**
- Leak detection trend charts
- Leak type distribution
- Historical data visualization
- Performance metrics

### ⚙️ **Settings Tab**
- API endpoint configuration
- Auto-refresh settings
- Sound alert preferences
- System preferences

## API Integration

The frontend integrates with the following AQUESA API endpoints:

### Monitoring Control
- `POST /api/start` - Start monitoring
- `POST /api/stop` - Stop monitoring

### Data Retrieval
- `GET /api/leaks` - Get leak events
- `GET /api/dwelling-ids` - Get all dwelling IDs
- `GET /api/dwelling-ids/statistics` - Get encoding statistics
- `GET /api/dwelling/{id}/data` - Get dwelling-specific data

## Features in Detail

### 🔄 **Auto-Refresh**
- Configurable refresh interval (5-300 seconds)
- Automatic data updates
- Connection status monitoring
- Error handling and retry logic

### 🔊 **Sound Alerts**
- Audio notifications for new leaks
- Configurable alert preferences
- Browser-based sound generation
- Non-intrusive alert system

### 📱 **Responsive Design**
- Mobile-friendly interface
- Bootstrap 5 framework
- Adaptive layouts
- Touch-friendly controls

### 🎨 **Modern UI/UX**
- Clean, professional design
- Intuitive navigation
- Color-coded status indicators
- Smooth animations and transitions

## Customization

### 🎨 **Styling**
Edit `styles.css` to customize:
- Color schemes
- Layout spacing
- Animation effects
- Responsive breakpoints

### ⚙️ **Configuration**
Modify `app.js` to adjust:
- API endpoints
- Refresh intervals
- Chart configurations
- Alert behaviors

### 🔧 **Functionality**
Extend `app.js` to add:
- New dashboard widgets
- Additional API integrations
- Custom data visualizations
- Enhanced filtering options

## Browser Compatibility

### ✅ **Supported Browsers**
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 📋 **Required Features**
- ES6 JavaScript support
- CSS Grid and Flexbox
- Web Audio API (for alerts)
- Local Storage API

## Troubleshooting

### 🔌 **Connection Issues**
1. Verify AQUESA API is running on port 8000
2. Check CORS settings in browser
3. Confirm network connectivity
4. Review browser console for errors

### 📊 **Data Loading Problems**
1. Check API endpoint URLs in settings
2. Verify API responses in browser dev tools
3. Confirm data format compatibility
4. Review JavaScript console for errors

### 🎨 **Display Issues**
1. Clear browser cache and cookies
2. Disable browser extensions
3. Check CSS loading in dev tools
4. Verify responsive design settings

## Development

### 🛠️ **Local Development**
```bash
# Start with auto-reload
python server.py --port 3000

# Use different port if needed
python server.py --port 3001
```

### 🔧 **Debugging**
- Open browser developer tools (F12)
- Check Console tab for JavaScript errors
- Monitor Network tab for API calls
- Use Elements tab for CSS debugging

### 📝 **Adding Features**
1. Update HTML structure in `index.html`
2. Add styles in `styles.css`
3. Implement functionality in `app.js`
4. Test across different browsers

## Performance

### ⚡ **Optimization Features**
- Efficient DOM updates
- Minimal API calls
- Cached data where appropriate
- Lazy loading for charts

### 📊 **Monitoring**
- Connection status indicators
- Error handling and reporting
- Performance metrics tracking
- User feedback systems

## Security

### 🔒 **Security Considerations**
- CORS-enabled for local development
- No sensitive data storage
- Client-side only authentication
- Secure API communication

### 🛡️ **Best Practices**
- Input validation on all forms
- XSS prevention measures
- Secure local storage usage
- Regular dependency updates

## Support

### 📞 **Getting Help**
- Check browser console for errors
- Review API server logs
- Verify network connectivity
- Test with different browsers

### 🐛 **Reporting Issues**
- Include browser version
- Provide error messages
- Describe reproduction steps
- Share relevant screenshots

---

**AQUESA Smart Water Leak Detection System**  
Modern web dashboard for real-time leak monitoring and management.
