#!/usr/bin/env python3
"""
Test script specifically for dwelling ID decoding functionality.
This script tests the advanced decoding of binary/corrupted dwelling IDs.
"""

import sys
import os
import uuid
import json

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from app.db.mongodb import decode_dwelling_id_advanced, decode_utf8_safely, get_db
    from app.core.services import get_all_dwelling_ids_utf8, process_dwelling_id_safely
    print("✅ Successfully imported decoding functions")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_corrupted_dwelling_ids():
    """Test decoding of the corrupted dwelling IDs from your example."""
    print("\n🔧 Testing corrupted dwelling ID decoding...")
    
    # Your example corrupted dwelling IDs
    corrupted_ids = [
        "2cee70f7-339f-4750-9bf3-eb4fede8f672",  # This one looks like a valid UUID
        "\tD+��\u0015M؄N�\u0014�=ͷ",
        "\u000e\u001d\u0010�>\u001dL%�\\�\"=�\n�",
        "\u001b��\u0016\u000e�G��J/\u0006���D",
        " 04pġJS�\u0002+9�㬕",
        "#d�P�N��lTS�\u0013Y",
        ",�p�3�GP���O���r",
        "B�5\u0013x�Fو���\u0016\u0007��",
        "K`����L��^���1��",
        "N{��V\u0006I����\\��ő",
        "]4�y�\u0012M5��$�lqB�",
        "���i��NЯI�8#�\"�",
        "��3&�{K?�\u0015\u0015TA:��",
        "�Q�(\u0015wB1�[��\u0003\u000b��",
        "��3t\u0001AG���>�^ar",
        "۪\u000b��[C��\u0011k1uXI\u0004",
        "�es.��G����M�~N�",
        "�ג�єMt��\\a\u00124�_",
        "�PD���M�����\u001f\u0002��"
    ]
    
    print("Decoding corrupted dwelling IDs:")
    print("=" * 80)
    print(f"{'Original (truncated)':<30} | {'Decoded':<40} | {'Type'}")
    print("-" * 80)
    
    decoded_results = []
    
    for i, corrupted_id in enumerate(corrupted_ids):
        try:
            # Test with advanced decoder
            decoded_advanced = decode_dwelling_id_advanced(corrupted_id)
            
            # Test with safe decoder for comparison
            decoded_safe = decode_utf8_safely(corrupted_id)
            
            # Truncate original for display
            original_display = repr(corrupted_id)[:28] + "..." if len(repr(corrupted_id)) > 30 else repr(corrupted_id)
            
            print(f"{original_display:<30} | {decoded_advanced:<40} | Advanced")
            
            decoded_results.append({
                'index': i,
                'original': corrupted_id,
                'decoded_advanced': decoded_advanced,
                'decoded_safe': decoded_safe,
                'is_uuid': is_valid_uuid(decoded_advanced)
            })
            
        except Exception as e:
            print(f"{'ERROR':<30} | {str(e):<40} | Error")
    
    return decoded_results

def is_valid_uuid(uuid_string):
    """Check if a string is a valid UUID."""
    try:
        uuid.UUID(uuid_string)
        return True
    except ValueError:
        return False

def test_binary_uuid_conversion():
    """Test conversion of binary data to UUID."""
    print("\n🧪 Testing binary UUID conversion...")
    
    # Create a test UUID and convert it to binary
    test_uuid = uuid.uuid4()
    uuid_bytes = test_uuid.bytes
    
    print(f"Original UUID: {test_uuid}")
    print(f"UUID bytes length: {len(uuid_bytes)}")
    print(f"UUID bytes (hex): {uuid_bytes.hex()}")
    
    # Test decoding the binary data
    decoded = decode_dwelling_id_advanced(uuid_bytes)
    print(f"Decoded from bytes: {decoded}")
    print(f"Matches original: {str(test_uuid) == decoded}")
    
    # Test with corrupted binary that might represent a UUID
    print("\nTesting with simulated corrupted binary data:")
    for corrupted_id in ["\tD+��\u0015M؄N�\u0014�=ͷ", "K`����L��^���1��"]:
        try:
            # Convert to bytes using latin1 to preserve binary data
            binary_data = corrupted_id.encode('latin1')
            print(f"Binary length: {len(binary_data)} bytes")
            print(f"Binary hex: {binary_data.hex()}")
            
            decoded = decode_dwelling_id_advanced(binary_data)
            print(f"Decoded: {decoded}")
            print(f"Is valid UUID: {is_valid_uuid(decoded)}")
            print("-" * 50)
        except Exception as e:
            print(f"Error processing: {e}")

def test_database_dwelling_ids():
    """Test fetching and decoding dwelling IDs from the actual database."""
    print("\n🗄️ Testing database dwelling ID fetching...")
    
    try:
        # Fetch dwelling IDs using the enhanced function
        dwelling_ids = get_all_dwelling_ids_utf8()
        
        print(f"Fetched {len(dwelling_ids)} dwelling IDs from database")
        
        if dwelling_ids:
            print("\nFirst 10 dwelling IDs:")
            for i, dwelling_id in enumerate(dwelling_ids[:10]):
                is_uuid = is_valid_uuid(dwelling_id)
                uuid_indicator = "✅ UUID" if is_uuid else "❌ Not UUID"
                print(f"{i+1:2d}. {dwelling_id} ({uuid_indicator})")
            
            # Count valid UUIDs
            valid_uuids = sum(1 for did in dwelling_ids if is_valid_uuid(did))
            print(f"\nStatistics:")
            print(f"Total dwelling IDs: {len(dwelling_ids)}")
            print(f"Valid UUIDs: {valid_uuids}")
            print(f"Non-UUID IDs: {len(dwelling_ids) - valid_uuids}")
            
            # Show examples of non-UUID IDs
            non_uuid_ids = [did for did in dwelling_ids if not is_valid_uuid(did)]
            if non_uuid_ids:
                print(f"\nExamples of non-UUID IDs:")
                for i, non_uuid_id in enumerate(non_uuid_ids[:5]):
                    print(f"  {i+1}. {repr(non_uuid_id)}")
        
    except Exception as e:
        print(f"❌ Database test error: {e}")

def test_process_dwelling_id_safely():
    """Test the process_dwelling_id_safely function."""
    print("\n🛡️ Testing process_dwelling_id_safely function...")
    
    test_cases = [
        "2cee70f7-339f-4750-9bf3-eb4fede8f672",  # Valid UUID
        "\tD+��\u0015M؄N�\u0014�=ͷ",  # Corrupted binary
        "normal_dwelling_id",  # Normal string
        b'\x2c\xee\x70\xf7\x33\x9f\x47\x50\x9b\xf3\xeb\x4f\xed\xe8\xf6\x72',  # UUID bytes
        None,  # None value
        123,  # Integer
    ]
    
    print("Testing process_dwelling_id_safely:")
    print("-" * 60)
    
    for i, test_case in enumerate(test_cases):
        try:
            result = process_dwelling_id_safely(test_case)
            input_repr = repr(test_case)[:40] + "..." if len(repr(test_case)) > 40 else repr(test_case)
            print(f"{i+1}. Input:  {input_repr}")
            print(f"   Output: {result}")
            print(f"   UUID:   {'✅' if is_valid_uuid(result) else '❌'}")
            print()
        except Exception as e:
            print(f"{i+1}. Input:  {repr(test_case)}")
            print(f"   Error:  {e}")
            print()

def save_results_to_file(decoded_results):
    """Save the decoding results to a JSON file for analysis."""
    try:
        # Convert results to JSON-serializable format
        json_results = []
        for result in decoded_results:
            json_result = {
                'index': result['index'],
                'original_repr': repr(result['original']),
                'decoded_advanced': result['decoded_advanced'],
                'decoded_safe': result['decoded_safe'],
                'is_uuid': result['is_uuid'],
                'original_length': len(result['original']),
                'decoded_length': len(result['decoded_advanced'])
            }
            json_results.append(json_result)
        
        with open('dwelling_id_decoding_results.json', 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved to dwelling_id_decoding_results.json")
        
    except Exception as e:
        print(f"❌ Error saving results: {e}")

def main():
    """Main test function."""
    print("🚀 Dwelling ID Decoding Test Suite")
    print("=" * 60)
    
    # Test corrupted dwelling IDs
    decoded_results = test_corrupted_dwelling_ids()
    
    # Test binary UUID conversion
    test_binary_uuid_conversion()
    
    # Test database dwelling IDs
    test_database_dwelling_ids()
    
    # Test the safe processing function
    test_process_dwelling_id_safely()
    
    # Save results
    save_results_to_file(decoded_results)
    
    print("\n✅ Dwelling ID decoding test suite completed!")
    print("\n📋 Summary:")
    print("- Advanced decoder handles binary UUID data")
    print("- Corrupted IDs are converted to readable format")
    print("- Database fetching uses enhanced decoding")
    print("- Results saved for further analysis")

if __name__ == "__main__":
    main()
