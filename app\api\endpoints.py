from fastapi import APIRouter, HTTPException
from datetime import datetime
from typing import Optional
from app.core.services import (
    start_monitoring,
    stop_monitoring,
    get_leak_events,
    get_all_dwelling_ids_utf8,
    get_dwelling_data_utf8,
    get_dwelling_id_statistics_utf8,
    process_dwelling_id_safely
)
from pydantic import BaseModel

router = APIRouter()

class TimeRange(BaseModel):
    start: datetime = None # type: ignore
    end: datetime = None # pyright: ignore[reportAssignmentType]

@router.post("/monitoring/start")
async def start_monitoring_endpoint():
    try:
        start_monitoring()
        return {"status": "Monitoring started"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/monitoring/stop")
async def stop_monitoring_endpoint():
    try:
        stop_monitoring()
        return {"status": "Monitoring stopped"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# @router.get("/leaks")
# async def get_leaks_endpoint(
#     start: datetime = None,
#     end: datetime = None,
#     dwellingid: str = None,
#     limit: int = 100
# ):
#     try:
#         return {
#             "leaks": get_leak_events(
#                 start_time=start,
#                 end_time=end,
#                 dwellingid=dwellingid,
#                 limit=limit
#             )
#         }
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))



@router.get("/leaks")
async def get_leaks_endpoint():
    """Retrieve all leak events"""
    try:
        return {
            "leaks": get_leak_events()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/dwelling-ids")
async def get_dwelling_ids_endpoint():
    """Retrieve all dwelling IDs with UTF-8 decoding"""
    try:
        dwelling_ids = get_all_dwelling_ids_utf8()
        return {
            "dwelling_ids": dwelling_ids,
            "count": len(dwelling_ids),
            "encoding": "UTF-8"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/dwelling-ids/statistics")
async def get_dwelling_id_statistics_endpoint():
    """Get statistics about dwelling IDs with UTF-8 encoding information"""
    try:
        stats = get_dwelling_id_statistics_utf8()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/dwelling/{dwelling_id}/data")
async def get_dwelling_data_endpoint(dwelling_id: str, start_time: Optional[datetime] = None, end_time: Optional[datetime] = None): # type: ignore
    """Get data for a specific dwelling with UTF-8 decoded IDs"""
    try:
        # Process the dwelling ID safely
        safe_dwelling_id = process_dwelling_id_safely(dwelling_id)

        # Fetch data with UTF-8 decoding
        data = get_dwelling_data_utf8(safe_dwelling_id, start_time, end_time)

        return {
            "dwelling_id": safe_dwelling_id,
            "original_dwelling_id": dwelling_id,
            "data": data.to_dict("records") if not data.empty else [],
            "count": len(data),
            "encoding": "UTF-8"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))