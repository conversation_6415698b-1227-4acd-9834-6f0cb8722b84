#!/usr/bin/env python3
"""
Test script to demonstrate UTF-8 ID fetching and decoding functionality
in the AQUESA leak detection system.
"""

import sys
import os
import asyncio
from datetime import datetime, timedelta

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from app.core.services import (
        get_all_dwelling_ids_utf8,
        get_dwelling_data_utf8,
        get_dwelling_id_statistics_utf8,
        process_dwelling_id_safely
    )
    from app.db.mongodb import (
        decode_utf8_safely,
        fetch_all_dwelling_ids,
        get_dwelling_id_mapping,
        get_db
    )
    print("✅ Successfully imported UTF-8 functions")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_utf8_decoding():
    """Test UTF-8 decoding functionality with various input types."""
    print("\n🧪 Testing UTF-8 decoding functionality...")
    
    # Test cases with different input types
    test_cases = [
        "normal_id_123",
        "dwelling_café_456",  # UTF-8 characters
        "id_with_émojis_🏠",  # Emojis
        b"bytes_id_789",  # Bytes input
        123,  # Integer input
        None,  # None input
        "",  # Empty string
        "id_with_ñ_and_ü",  # Special characters
    ]
    
    print("Input → UTF-8 Decoded Output:")
    print("-" * 50)
    
    for test_input in test_cases:
        try:
            decoded = decode_utf8_safely(test_input)
            input_type = type(test_input).__name__
            print(f"{str(test_input):<20} ({input_type:<8}) → {decoded}")
        except Exception as e:
            print(f"{str(test_input):<20} → ERROR: {e}")

def test_dwelling_id_processing():
    """Test dwelling ID processing with UTF-8 safety."""
    print("\n🏠 Testing dwelling ID processing...")
    
    test_ids = [
        "DW001",
        "dwelling_café_001",
        "🏠_house_123",
        b"binary_id_456",
    ]
    
    print("Original ID → Processed ID:")
    print("-" * 40)
    
    for dwelling_id in test_ids:
        try:
            processed = process_dwelling_id_safely(dwelling_id)
            print(f"{str(dwelling_id):<20} → {processed}")
        except Exception as e:
            print(f"{str(dwelling_id):<20} → ERROR: {e}")

async def test_database_operations():
    """Test database operations with UTF-8 decoding."""
    print("\n🗄️ Testing database operations...")
    
    try:
        # Test fetching all dwelling IDs
        print("Fetching all dwelling IDs with UTF-8 decoding...")
        dwelling_ids = get_all_dwelling_ids_utf8()
        print(f"Found {len(dwelling_ids)} dwelling IDs")
        
        if dwelling_ids:
            print("Sample dwelling IDs:")
            for i, dwelling_id in enumerate(dwelling_ids[:5]):  # Show first 5
                print(f"  {i+1}. {dwelling_id} (type: {type(dwelling_id).__name__})")
            
            # Test fetching data for the first dwelling
            if dwelling_ids:
                test_dwelling = dwelling_ids[0]
                print(f"\nFetching data for dwelling: {test_dwelling}")
                
                # Get data from the last 24 hours
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=24)
                
                data = get_dwelling_data_utf8(test_dwelling, start_time, end_time)
                print(f"Retrieved {len(data)} records for dwelling {test_dwelling}")
                
                if not data.empty:
                    print("Sample data columns:", list(data.columns))
                    if 'dwellingid' in data.columns:
                        unique_ids = data['dwellingid'].unique()
                        print(f"Unique dwelling IDs in data: {unique_ids}")
        
        # Test dwelling ID statistics
        print("\nGetting dwelling ID statistics...")
        stats = get_dwelling_id_statistics_utf8()
        print("Statistics:")
        for key, value in stats.items():
            if key != 'dwelling_ids':  # Don't print the full list
                print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"❌ Database operation error: {e}")

def test_direct_database_functions():
    """Test direct database functions."""
    print("\n🔧 Testing direct database functions...")
    
    try:
        db = get_db()
        collection = db["raw_data_ts"]
        
        print("Testing fetch_all_dwelling_ids...")
        dwelling_ids = fetch_all_dwelling_ids(collection)
        print(f"Direct fetch returned {len(dwelling_ids)} dwelling IDs")
        
        print("Testing get_dwelling_id_mapping...")
        id_mapping = get_dwelling_id_mapping(collection)
        print(f"ID mapping contains {len(id_mapping)} entries")
        
        if id_mapping:
            print("Sample ID mappings:")
            for i, (original, decoded) in enumerate(list(id_mapping.items())[:3]):
                print(f"  {original} → {decoded}")
        
    except Exception as e:
        print(f"❌ Direct database function error: {e}")

def main():
    """Main test function."""
    print("🚀 AQUESA UTF-8 ID Fetching Test Suite")
    print("=" * 50)
    
    # Test UTF-8 decoding
    test_utf8_decoding()
    
    # Test dwelling ID processing
    test_dwelling_id_processing()
    
    # Test database operations
    print("\nRunning database tests...")
    try:
        asyncio.run(test_database_operations())
    except Exception as e:
        print(f"❌ Async test error: {e}")
    
    # Test direct database functions
    test_direct_database_functions()
    
    print("\n✅ Test suite completed!")
    print("\n📋 Summary:")
    print("- UTF-8 decoding functions are working")
    print("- Dwelling ID processing handles various input types")
    print("- Database operations include UTF-8 safety")
    print("- All IDs are fetched and decoded in UTF-8 format")

if __name__ == "__main__":
    main()
